# matchPaymentScene 方法执行步骤详细分析

## 概述
`matchPaymentScene` 是支付场景匹配服务的核心入口方法，负责根据用户请求参数匹配合适的支付场景，并筛选出可用的支付方式。

## 方法调用链路图

```
CashierManagerService.getPayways()
    ↓
PaymentSceneMatchService.matchPaymentScene(ReqContext, RespContext)
    ↓
├── PaymentSceneMatchService.matchPaymentScene(ReqContext) [私有方法]
│   ├── ConfigCacheService.getAllPaymentScenes()
│   ├── ExprService.parseGroup(matchGroup, context)
│   │   └── EvalGroupService.eval(groupRel, context)
│   │       ├── ConfigCacheService.getByGroupRel(groupRel)
│   │       └── EvalRuleService.eval(ruleRel, context)
│   │           ├── ConfigCacheService.getByRuleRel(ruleRel)
│   │           └── CondExpr.eval(context)
│   └── ExprService.parseRule(matchRule, context)
│       └── EvalRuleService.eval(ruleRel, context)
├── ConfigCacheService.getByPaymentSceneCode(paymentSceneCode)
└── 支付方式过滤逻辑
```

## 详细执行步骤

### 第一阶段：初始化和参数准备
1. **获取请求参数**
   - 从 `ReqContext` 中提取 `PaywayRequest` 对象
   - 包含业务类型(businessType)、来源(source)等关键参数

2. **调用支付场景匹配**
   - 调用私有方法 `matchPaymentScene(ReqContext reqContext)`
   - 传入完整的请求上下文

### 第二阶段：支付场景匹配核心逻辑
3. **获取所有支付场景配置**
   ```java
   List<PaymentScene> allPaymentScenes = configCacheService.getAllPaymentScenes();
   ```
   - 从缓存中获取所有配置的支付场景
   - 缓存有效期：5分钟

4. **获取表达式上下文**
   ```java
   ExprContext context = reqContext.getExprContext();
   ```
   - 包含所有用于条件匹配的变量和上下文信息

5. **遍历支付场景进行匹配**
   - 按配置顺序逐个检查每个支付场景
   - 对每个支付场景执行以下步骤：

6. **提取匹配条件**
   ```java
   String matchGroup = paymentScene.getMatchGroup();
   String matchRule = paymentScene.getMatchRule();
   ```
   - matchGroup: 组合条件表达式
   - matchRule: 原子条件表达式

7. **空值检查**
   ```java
   if (StringUtils.isBlank(matchGroup) && StringUtils.isBlank(matchRule)) {
       // 如果都是空，认为不匹配，跳出循环
       break;
   }
   ```

8. **执行组合条件匹配**
   ```java
   boolean groupMatch = expressionService.parseGroup(matchGroup, context);
   ```
   - 解析组合条件表达式
   - 支持多个条件的逻辑组合

9. **执行原子条件匹配**
   ```java
   boolean ruleMatch = expressionService.parseRule(matchRule, context);
   ```
   - 解析原子条件表达式
   - 执行具体的条件判断

10. **综合匹配结果**
    ```java
    boolean isMatch = groupMatch && ruleMatch;
    ```
    - 两个条件都为true才算匹配成功

11. **匹配成功处理**
    - 如果匹配成功，记录匹配的支付场景
    - 跳出循环，不再检查后续场景

### 第三阶段：支付场景验证和错误处理
12. **匹配结果验证**
    ```java
    if (paymentScene == null) {
        log.error("没有匹配到支付场景 businessType={} source={}", req.getBusinessType(), req.getSource());
        return;
    }
    ```

13. **查询支付场景对应的支付方式**
    ```java
    List<PaymentScenePayway> paymentScenePayways = configCacheService.getByPaymentSceneCode(paymentScene.getPaymentSceneCode());
    ```
    - 根据匹配到的支付场景代码查询可用的支付方式

14. **支付方式配置验证**
    ```java
    if (CollectionUtils.isEmpty(paymentScenePayways)) {
        log.error("支付场景下没有配置支付方式. paymentSceneCode={}", paymentScene.getPaymentSceneCode());
        return;
    }
    ```

### 第四阶段：结果设置和支付方式过滤
15. **设置响应上下文**
    ```java
    respContext.setPaymentScene(paymentScene);
    respContext.setPaymentScenePayways(paymentScenePayways);
    ```

16. **提取允许的支付方式代码**
    ```java
    List<String> matchedPaywayCodes = paymentScenePayways.stream()
        .map(PaymentScenePayway::getPaymentWayCode)
        .collect(Collectors.toList());
    ```

17. **过滤支付方式**
    ```java
    List<Payway> filterPayways = payways.stream()
        .filter(payway -> {
            String paywayCode = payway.getPaywayCode();
            return matchedPaywayCodes.contains(paywayCode);
        }).collect(Collectors.toList());
    ```
    - 只保留支付场景下允许的支付方式

18. **更新请求上下文**
    ```java
    reqContext.setPayways(filterPayways);
    ```

## 关键数据结构

### PaymentScene (支付场景)
- `paymentSceneCode`: 支付场景代码
- `matchGroup`: 组合匹配条件
- `matchRule`: 原子匹配条件

### PaymentScenePayway (支付场景-支付方式关联)
- `paymentSceneCode`: 支付场景代码
- `paymentWayCode`: 支付方式代码
- `filterGroup`: 过滤组合条件
- `filterRule`: 过滤原子条件
- `actionGroup`: 动作组合条件
- `actionRule`: 动作原子条件

### ExprContext (表达式上下文)
- `varMap`: 变量映射表
- `reqContext`: 请求上下文
- `respContext`: 响应上下文
- `payway`: 当前处理的支付方式

## 异常处理机制
- 所有数据库查询异常都会被捕获并记录日志
- 匹配失败时返回null，上层调用者负责处理
- 表达式解析异常会被捕获，返回false

## 性能优化点
- 使用Guava缓存机制，避免频繁数据库查询
- 缓存有效期5分钟，平衡数据一致性和性能
- 按顺序匹配，找到第一个匹配的场景即停止

## 日志记录
- DEBUG级别：记录匹配过程的详细信息
- ERROR级别：记录匹配失败和配置错误
- 包含关键业务参数便于问题排查
